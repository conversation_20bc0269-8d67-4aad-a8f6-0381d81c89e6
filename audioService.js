
import * as dom from './domElements.js';
import * as state from './state.js';
import * as ui from './ui.js';
import { formatTime } from './utils.js';
import { findAndSetCorrespondingDocument } from './documentService.js';
import { updateWordHighlight } from './highlightService.js';


export async function selectMusicTrack(trackId, resumePlay = true) {
    // If it's the same track and we don't intend to force a reload (e.g. by resumePlay=false), do nothing.
    // Or if it's playing and we want to resume, it implies a reload if trackId is current.
    if (state.currentTrackId === trackId && state.isPlaying && resumePlay && dom.audioPlayer.src) return;


    const trackToPlay = state.musicFiles.find(mf => mf.id === trackId);
    if (!trackToPlay) return;

    const oldTrackId = state.currentTrackId;
    state.setCurrentTrackId(trackId);
    state.setCurrentAudioFile(trackToPlay);
    
    // Revoke previous object URL if it was synthesized AND it's different from current
    // Also, ensure we don't revoke if the new track is the same as the old one but we are forcing a reload.
    if (dom.audioPlayer.src && dom.audioPlayer.dataset.isSynthesized === 'true') {
        // Only revoke if the URL will actually change, or if we are explicitly changing tracks
        if (oldTrackId !== trackId || dom.audioPlayer.src !== trackToPlay.objectURL) {
             // Check if the src is an object URL before revoking
            if (dom.audioPlayer.src.startsWith('blob:')) {
                URL.revokeObjectURL(dom.audioPlayer.src);
                console.log("Revoked ObjectURL in selectMusicTrack:", dom.audioPlayer.src);
            }
        }
    }
    
    dom.audioPlayer.src = trackToPlay.objectURL;
    dom.audioPlayer.dataset.isSynthesized = trackToPlay.isSynthesized.toString();

    dom.audioPlayer.load();
    if (resumePlay) {
        dom.audioPlayer.play().catch(e => console.error("Error playing track:", e));
    }
    
    state.setIsPausedManually(false); // Assume play will start or is intended
    if (resumePlay) {
      ui.updateStatus(`Playing: ${trackToPlay.name}`);
    } else {
      ui.updateStatus(`Selected: ${trackToPlay.name}. Press Play.`);
    }
    ui.renderMusicPlaylist();
    ui.updateAudioControlsUI(); // This will also update play/pause button based on state.isPlaying
    await findAndSetCorrespondingDocument(trackId);
}

export function handleAudioLoadedMetadata() {
    dom.durationDisplay.textContent = formatTime(dom.audioPlayer.duration);
    dom.progressBar.max = dom.audioPlayer.duration;
}

export function handleAudioTimeUpdate() {
    dom.currentTimeDisplay.textContent = formatTime(dom.audioPlayer.currentTime);
    dom.progressBar.value = dom.audioPlayer.currentTime;
    updateWordHighlight();
}

export function handleAudioEnded() {
    playNextTrack();
}

export function togglePlayPause() {
    if (!state.currentTrackId && state.musicFiles.length > 0) {
        selectMusicTrack(state.musicFiles[0].id);
        return;
    }
    if (!state.currentAudioFile) return;

    if (state.isPlaying && !state.isPausedManually) {
        dom.audioPlayer.pause();
        state.setIsPausedManually(true);
        ui.updateStatus('Paused.');
    } else {
        dom.audioPlayer.play().catch(e => console.error("Error in togglePlayPause:", e));
        state.setIsPausedManually(false);
        // Status updated by 'play' event which sets isPlaying
    }
    // isPlaying state is set by audio element's play/pause events, so we don't set it here directly.
    // updatePlayPauseButton will be called by those events.
}

export function playNextTrack() {
    if (state.musicFiles.length === 0) return;
    const currentIndex = state.musicFiles.findIndex(mf => mf.id === state.currentTrackId);
    let nextIndex = (currentIndex + 1) % state.musicFiles.length;
    if (state.musicFiles[nextIndex]) {
        selectMusicTrack(state.musicFiles[nextIndex].id);
    }
}

export function playPrevTrack() {
    if (state.musicFiles.length === 0) return;
    const currentIndex = state.musicFiles.findIndex(mf => mf.id === state.currentTrackId);
    let prevIndex = (currentIndex - 1 + state.musicFiles.length) % state.musicFiles.length;
     if (state.musicFiles[prevIndex]) {
        selectMusicTrack(state.musicFiles[prevIndex].id);
    }
}

export function handleSeek(event) {
    if (state.currentAudioFile) {
        dom.audioPlayer.currentTime = Number(event.target.value);
    }
}

export function handleVolumeChange(event) {
    const newVolume = Number(event.target.value);
    dom.audioPlayer.volume = newVolume;
    if (newVolume === 0) dom.volumeIcon.textContent = 'volume_off';
    else if (newVolume < 0.5) dom.volumeIcon.textContent = 'volume_down';
    else dom.volumeIcon.textContent = 'volume_up';
}

export function stopAudioPlayer() {
    dom.audioPlayer.pause();
    if (dom.audioPlayer.src && dom.audioPlayer.src.startsWith('blob:')) {
        // Optionally revoke here if stopping means we won't use this src again soon
        // For now, let selectMusicTrack handle revocation primarily
        // URL.revokeObjectURL(dom.audioPlayer.src);
    }
    dom.audioPlayer.src = ""; // Clear the source
    state.setIsPlaying(false);
    state.setIsPausedManually(true); // Mark as paused
    ui.updatePlayPauseButton();
    dom.currentTimeDisplay.textContent = formatTime(0);
    dom.durationDisplay.textContent = formatTime(0);
    dom.progressBar.value = 0;
    dom.progressBar.max = 0;
}
