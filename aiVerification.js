import * as dom from './domElements.js';
import * as state from './state.js';
import * as ttsService from './ttsService.js';
import * as documentService from './documentService.js';
import * as audioService from './audioService.js';
import * as ui from './ui.js';
import { G_TTS_API_KEY, MS_TTS_API_KEY, MS_TTS_SERVICE_REGION } from './constants.js';
import { ssmlEscapeXmlEntities } from './utils.js';

// Hold references to UI functions passed during init
let uiUpdateStatus = () => {};
let uiShowSSMLModal = () => {};
let uiHideSSMLModal = () => {};
let uiRenderVerificationResults = () => {};
let uiUpdateAudioControlsUI = () => {};
let uiUpdateVerificationOverallStatus = () => {};


// --- AI Verification Feature ---
export async function getCorrespondingTextContent(audioFile) {
    const audioBaseName = audioFile.name
        .replace(/^\((G-TTS|MS-TTS)\)\s*/i, '')
        .substring(0, audioFile.name.replace(/^\((G-TTS|MS-TTS)\s*/i, '').lastIndexOf('.'))
        .toLowerCase();

    const docFileEntry = state.docFiles.find(df =>
        df.name.substring(0, df.name.lastIndexOf('.')).toLowerCase() === audioBaseName
    );

    if (!docFileEntry) return null;

    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async (e) => {
            if (docFileEntry.type === 'txt') {
                resolve(e.target.result);
            } else if (docFileEntry.type === 'docx') {
                if (window.mammoth) {
                    try {
                        const result = await window.mammoth.extractRawText({ arrayBuffer: e.target.result });
                        resolve(result.value);
                    } catch (err) {
                        console.error('Error extracting text from DOCX for verification:', err);
                        reject(new Error("Error extracting DOCX text."));
                    }
                } else {
                    reject(new Error("Mammoth.js not available for DOCX."))
                }
            }
        };
        reader.onerror = () => reject(new Error(`Error reading document file ${docFileEntry.name}`));
        
        if (docFileEntry.type === 'txt') reader.readAsText(docFileEntry.file);
        else if (docFileEntry.type === 'docx') reader.readAsArrayBuffer(docFileEntry.file);
    });
}

async function simulateWhisperTranscription(audioFile, originalText) {
    await new Promise(resolve => setTimeout(resolve, 700)); 
    if (!originalText) return "Simulated: No original text to compare.";
    let transcript = originalText;
    if (transcript.includes("the")) {
        transcript = transcript.replace(" the ", " thee "); 
    } else if (transcript.length > 20) {
        transcript = transcript.substring(0, 10) + " (simulated change) " + transcript.substring(10);
    }
    return transcript.substring(0, 200);
}

async function simulateAIEditForPronunciation(originalText, whisperTranscript) {
    await new Promise(resolve => setTimeout(resolve, 500));
    let identifiedIssues = 0;
    let ssmlTextContent = originalText; 

    if (whisperTranscript && whisperTranscript.includes("thee") && originalText.includes("the ")) {
        const words = originalText.split(' ');
        const wordToChange = words.find(w => w.toLowerCase() === "the");
        if (wordToChange) {
             ssmlTextContent = originalText.replace(new RegExp(`\\b${ssmlEscapeXmlEntities(wordToChange)}\\b`, 'gi'), 
                `<phoneme alphabet="ipa" ph="ðə">${ssmlEscapeXmlEntities(wordToChange)}</phoneme>`);
            identifiedIssues++;
        }
    }
    else if (originalText.length > 10) {
        const words = originalText.split(/\s+/);
        if (words.length > 2) {
            const targetWord = words[1]; 
            ssmlTextContent = originalText.replace(new RegExp(`\\b${ssmlEscapeXmlEntities(targetWord)}\\b`), 
                `<phoneme alphabet="ipa" ph="sɪm.jə.leɪ.tɪd">${ssmlEscapeXmlEntities(targetWord)}</phoneme>`);
            identifiedIssues++;
        }
    }
    
    let finalSSMLString;
    if (identifiedIssues > 0) {
        finalSSMLString = `<speak>${ssmlTextContent}</speak>`;
    } else {
        finalSSMLString = `<speak>${ssmlEscapeXmlEntities(originalText)}</speak>`;
    }

    return { 
        isFlagged: identifiedIssues > 0, 
        ssmlText: finalSSMLString, 
        identifiedIssuesCount: identifiedIssues 
    };
}


export async function handleStartVerification() {
    if (state.isVerifying) {
        uiUpdateStatus("Verification is already in progress.");
        return;
    }
    if (state.musicFiles.length === 0) {
        uiUpdateStatus("No music files loaded to verify.");
        return;
    }

    state.setIsVerifying(true);
    state.setVerificationResults([]);
    state.setCurrentVerificationFileIndex(0);
    dom.verificationProcessPanel.style.display = 'flex';
    uiUpdateAudioControlsUI(); 
    uiRenderVerificationResults(); 

    for (let i = 0; i < state.musicFiles.length; i++) {
        state.setCurrentVerificationFileIndex(i);
        const audioFile = state.musicFiles[i];
        let resultEntry = {
            originalAudioFile: { name: audioFile.name, id: audioFile.id },
            status: "Processing...",
            replaced: false, 
        };
        state.verificationResults.push(resultEntry);
        uiRenderVerificationResults(); 

        try {
            const originalTextContent = await getCorrespondingTextContent(audioFile);
            if (!originalTextContent) {
                resultEntry.status = "No text match";
                uiRenderVerificationResults();
                continue;
            }

            const whisperTranscript = await simulateWhisperTranscription(audioFile, originalTextContent);
            resultEntry.whisperTranscriptPreview = whisperTranscript.substring(0, 50); 

            const aiEditResult = await simulateAIEditForPronunciation(originalTextContent, whisperTranscript);
            resultEntry.aiAnalysis = { 
                identifiedIssues: aiEditResult.identifiedIssuesCount, 
                ssmlText: aiEditResult.ssmlText 
            };

            if (aiEditResult.isFlagged) {
                resultEntry.status = "Flagged - Needs Reprocessing";
                uiRenderVerificationResults();

                const preferredTTSProvider = dom.gTtsVoiceSelect.value && G_TTS_API_KEY ? 'google' : (dom.msTtsVoiceSelect.value && MS_TTS_API_KEY && MS_TTS_SERVICE_REGION ? 'microsoft' : null);
                if (!preferredTTSProvider) {
                    resultEntry.status = "Error: No TTS provider configured/selected for reprocessing.";
                    uiRenderVerificationResults();
                    continue;
                }
                
                const voiceName = preferredTTSProvider === 'google' ? state.selectedGTTSVoiceName : state.selectedMsTTSVoiceName;
                const originalBaseName = audioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/i, '').substring(0, audioFile.name.replace(/^\((G-TTS|MS-TTS)\s*/i, '').lastIndexOf('.'));
                const originalFileExtension = audioFile.name.substring(audioFile.name.lastIndexOf('.')).toLowerCase();

                try {
                    resultEntry.status = `Correcting with ${preferredTTSProvider.toUpperCase()}...`;
                    uiRenderVerificationResults();

                    const reprocessedAudio = await ttsService.synthesizeTextToAudio(
                        aiEditResult.ssmlText, 
                        voiceName, 
                        preferredTTSProvider,
                        originalBaseName,
                        originalFileExtension
                    );
                    resultEntry.reprocessedAudio = reprocessedAudio;
                    resultEntry.status = "Corrected & Reprocessed";
                } catch (ttsError) {
                    console.error("TTS Reprocessing Error:", ttsError);
                    resultEntry.status = `Error in ${preferredTTSProvider.toUpperCase()} TTS`;
                }

            } else {
                resultEntry.status = "Checked - OK";
            }
        } catch (error) {
            console.error(`Error verifying ${audioFile.name}:`, error);
            resultEntry.status = "Error during verification";
        }
        uiRenderVerificationResults(); 
    }

    state.setIsVerifying(false);
    state.setCurrentVerificationFileIndex(-1);
    uiUpdateAudioControlsUI(); 
    uiUpdateVerificationOverallStatus(`Verification Complete. ${state.musicFiles.length} files processed.`);
    console.log("Verification complete.", state.verificationResults);
}

export function playVerificationPreviewAudio(objectURL) {
    if (state.verificationPreviewAudioURL && state.verificationPreviewAudioURL !== objectURL) {
        // URL.revokeObjectURL(state.verificationPreviewAudioURL); // Managed by player
    }
    state.setVerificationPreviewAudioURL(objectURL);
    dom.verificationPreviewAudioPlayer.src = objectURL;
    dom.verificationPreviewAudioPlayer.style.display = 'block';
    dom.verificationPreviewAudioPlayer.controls = true;
    dom.verificationPreviewAudioPlayer.play().catch(e => console.error("Error playing verification preview:", e));
}

export async function handleReplaceOriginalFile(originalAudioFileId) {
    const result = state.verificationResults.find(r => r.originalAudioFile.id === originalAudioFileId);
    if (!result || !result.reprocessedAudio || !result.aiAnalysis || !result.aiAnalysis.ssmlText) {
        uiUpdateStatus("Error: Cannot replace. Verification data is missing or incomplete.");
        console.error("Replacement error: Verification data missing for ID", originalAudioFileId, result);
        return;
    }
    if (result.replaced) {
        uiUpdateStatus("File already replaced.");
        return;
    }

    const originalAudioIndex = state.musicFiles.findIndex(mf => mf.id === result.originalAudioFile.id);
    if (originalAudioIndex === -1) {
        uiUpdateStatus(`Error: Original audio file "${result.originalAudioFile.name}" not found in playlist.`);
        console.error("Replacement error: Original audio file not found for ID", result.originalAudioFile.id);
        return;
    }
    const originalAudioEntry = state.musicFiles[originalAudioIndex];

    const originalAudioBaseName = result.originalAudioFile.name
        .replace(/^\((G-TTS|MS-TTS)\)\s*/i, '')
        .substring(0, result.originalAudioFile.name.replace(/^\((G-TTS|MS-TTS)\s*/i, '').lastIndexOf('.'))
        .toLowerCase();

    const originalDocIndex = state.docFiles.findIndex(df =>
        df.name.substring(0, df.name.lastIndexOf('.')).toLowerCase() === originalAudioBaseName
    );

    const reprocessedAudioData = result.reprocessedAudio;
    const newAudioFile = reprocessedAudioData.file; 
    const updatedAudioName = reprocessedAudioData.name.replace(/\(-Verified\)/i, '').replace(/^\((G-TTS|MS-TTS)\)\s*/i, `(${reprocessedAudioData.ttsProvider.toUpperCase()}) `).trim();


    const updatedAudioEntry = {
        ...originalAudioEntry, 
        name: updatedAudioName,
        file: newAudioFile,
        objectURL: reprocessedAudioData.objectURL, 
        duration: reprocessedAudioData.duration,
        isSynthesized: true,
        ttsProvider: reprocessedAudioData.ttsProvider,
    };

    if (originalAudioEntry.objectURL && originalAudioEntry.objectURL !== updatedAudioEntry.objectURL) {
        if (originalAudioEntry.objectURL.startsWith('blob:')) {
            URL.revokeObjectURL(originalAudioEntry.objectURL);
        }
        console.log("Revoked old objectURL for replaced audio:", originalAudioEntry.name);
    }
    state.musicFiles[originalAudioIndex] = updatedAudioEntry;

    if (originalDocIndex !== -1) {
        const originalDocEntry = state.docFiles[originalDocIndex];
        let docContentToSave = result.aiAnalysis.ssmlText; 

        const newDocName = originalDocEntry.name.substring(0, originalDocEntry.name.lastIndexOf('.')) + '.txt'; 
        const newDocFile = new File([docContentToSave], newDocName, { type: 'text/plain;charset=utf-8' });

        const updatedDocEntry = {
            ...originalDocEntry, 
            name: newDocName,
            file: newDocFile,
            type: 'txt', 
        };
        state.docFiles[originalDocIndex] = updatedDocEntry;
        
        if (state.currentDocId === updatedDocEntry.id) {
            await documentService.selectDocument(updatedDocEntry.id);
        }
    } else {
        uiUpdateStatus(`Warning: No corresponding document found to replace for "${result.originalAudioFile.name}". Audio file replaced.`);
    }
    
    result.replaced = true;
    result.status = "Replaced with Correction"; 

    ui.renderMusicPlaylist();
    ui.renderDocList();
    uiRenderVerificationResults(); 

    if (state.currentTrackId === updatedAudioEntry.id) {
        const currentPlaying = state.isPlaying;
        audioService.stopAudioPlayer();
        state.setCurrentAudioFile(null);
        await audioService.selectMusicTrack(updatedAudioEntry.id, currentPlaying);
    }

    uiUpdateStatus(`Successfully replaced "${result.originalAudioFile.name}" and its document with corrected versions.`);
    console.log("Replacement successful for", result.originalAudioFile.name);
}

export function initAiVerificationEventListeners(uiHelpers) {
    uiUpdateStatus = uiHelpers.updateStatus;
    uiShowSSMLModal = uiHelpers.showSSMLModal;
    uiHideSSMLModal = uiHelpers.hideSSMLModal;
    uiRenderVerificationResults = uiHelpers.renderVerificationResults;
    uiUpdateAudioControlsUI = uiHelpers.updateAudioControlsUI;
    uiUpdateVerificationOverallStatus = uiHelpers.updateVerificationOverallStatus;

    dom.startVerificationBtn.addEventListener('click', handleStartVerification);
    dom.viewSSMLTextModalCloseBtn.addEventListener('click', () => uiHideSSMLModal(dom.viewSSMLTextModal));
}