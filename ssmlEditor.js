
import * as dom from './domElements.js';
import * as state from './state.js';
import { SUPPORTED_DOC_EXTENSIONS } from './constants.js';
import { ssmlEscapeXmlEntities, downloadTextFile } from './utils.js';

// --- SSML Editor Constants ---
const SSML_BREAKPOINT_MARKER = "<!--BREAKPOINT-->";
let ssmlMaxLength = 5000;
const SSML_MIN_MAX_LENGTH = 1000;
let ssmlLongSentenceThreshold = 15;
const SSML_MIN_LONG_SENTENCE_THRESHOLD = 5;
let ssmlLastSearchIndex = 0;

// Hold references to UI functions passed during init
let uiShowSSMLModal = () => {};
let uiHideSSMLModal = () => {};
let uiUpdateSSMLStatusBar = () => {};


// --- SSML Editor Core Logic ---

function ssmlInsertText(textToInsert) {
    const textarea = dom.ssmlTextWidget;
    const oldScrollTop = textarea.scrollTop;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentText = textarea.value;

    textarea.value = currentText.substring(0, start) + textToInsert + currentText.substring(end);
    
    const newCursorPos = start + textToInsert.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus({ preventScroll: true });
    textarea.scrollTop = oldScrollTop;

    requestAnimationFrame(() => {
       textarea.scrollTop = oldScrollTop;
    });

    uiUpdateSSMLStatusBar(`Inserted: ${textToInsert.length > 20 ? textToInsert.substring(0,17)+'...' : textToInsert}`);
}

function ssmlWrapSelectedText(startTag, endTag) {
    const textarea = dom.ssmlTextWidget;
    const oldScrollTop = textarea.scrollTop;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    if (start === end) {
        uiShowSSMLModal(dom.ssmlPreviewModal); 
        dom.ssmlPreviewValidationStatus.textContent = "Selection Required";
        dom.ssmlPreviewValidationStatus.style.color = "red";
        dom.ssmlPreviewCharCount.textContent = "Please select text to wrap.";
        dom.ssmlPreviewContent.innerHTML = ""; 
        console.warn("No text selected for SSML wrapping.");
        uiUpdateSSMLStatusBar("No text selected to wrap.");
        return;
    }

    const selectedText = textarea.value.substring(start, end);
    const newText = `${startTag}${selectedText}${endTag}`;
    textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);

    const newSelectionEnd = start + newText.length;
    textarea.setSelectionRange(start, newSelectionEnd);
    textarea.focus({ preventScroll: true });
    textarea.scrollTop = oldScrollTop;
    
    requestAnimationFrame(() => {
       textarea.scrollTop = oldScrollTop;
    });

    uiUpdateSSMLStatusBar(`Wrapped selection with ${startTag}...${endTag}`);
}

function ssmlAutoPause(pattern, entityName) {
    const textarea = dom.ssmlTextWidget;
    const oldScrollTop = textarea.scrollTop;
    const oldSelectionStart = textarea.selectionStart;
    const oldSelectionEnd = textarea.selectionEnd;

    const content = textarea.value;
    const breakTagString = '<break time="500ms"/>';
    const escapedBreakTagString = breakTagString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    const regex = new RegExp(
        `(?<!${escapedBreakTagString}\\s)` + 
        `\\b(${pattern.source})\\b` +       
        `([.,!?:;]*)` +                     
        `(?!\\s${escapedBreakTagString})`,   
        'gi'
    );

    let replacements = 0;
    const newContent = content.replace(regex, (match, p1, p2) => {
        replacements++;
        return `${breakTagString} ${p1}${p2} ${breakTagString}`;
    });

    if (replacements > 0) {
        textarea.value = newContent;
        const newLength = newContent.length;
        const newCursorPos = Math.min(oldSelectionStart + (newContent.length - content.length) / replacements, newLength); 
        textarea.setSelectionRange(Math.min(oldSelectionStart, newLength), Math.min(oldSelectionEnd + (newContent.length - content.length), newLength));
        textarea.scrollTop = oldScrollTop;
        textarea.focus({ preventScroll: true });
        requestAnimationFrame(() => {
           textarea.scrollTop = oldScrollTop;
        });
        const message = `Inserted <break time="500ms"/> around ${replacements} occurrence(s) of "${entityName}".`;
        uiUpdateSSMLStatusBar(message);
        alert(message);
    } else {
        const message = `No instances of "${entityName}" found or they are already correctly formatted.`;
        uiUpdateSSMLStatusBar(message);
        alert(message);
        textarea.scrollTop = oldScrollTop;
        textarea.focus({ preventScroll: true });
         requestAnimationFrame(() => {
           textarea.scrollTop = oldScrollTop;
        });
    }
}

function ssmlTokenizeSentences(text) {
    if (!text) return [];
    const sentences = text.match(/([^\.!\?]+(?:www\.[^\s]+|[a-z]\.[a-z\.])*[^\.!\?]*[\.!\?]+(?:["']|\u201D|\u2019)?\s+)|([^\.!\?]+$)/g);
    return sentences ? sentences.map(s => s.trim()).filter(s => s.length > 0) : [text]; 
}


function ssmlAutoBreakLongSentences() {
    const textarea = dom.ssmlTextWidget;
    const oldScrollTop = textarea.scrollTop;
    const oldSelectionStart = textarea.selectionStart;
    const oldSelectionEnd = textarea.selectionEnd;
    
    const content = textarea.value;
    const sentences = ssmlTokenizeSentences(content);
    let newContent = content;
    let breaksInserted = 0;
    let lastReplacementEnd = 0;

    sentences.forEach(sentence => {
        const originalSentenceIndex = newContent.indexOf(sentence, lastReplacementEnd);
        if (originalSentenceIndex === -1) return; 

        const words = sentence.split(/\s+/).filter(w => w.length > 0);
        if (words.length > ssmlLongSentenceThreshold &&
            sentence.match(/^[A-Z"“«¿¡{\[]/) && 
            !sentence.trim().startsWith("<break")) {
            
            const prefix = newContent.substring(0, originalSentenceIndex);
            const suffix = newContent.substring(originalSentenceIndex + sentence.length);
            newContent = `${prefix}<break time="500ms"/> ${sentence.trim()}${suffix}`;
            
            breaksInserted++;
            lastReplacementEnd = originalSentenceIndex + `<break time="500ms"/> ${sentence.trim()}`.length;
        } else {
            lastReplacementEnd = originalSentenceIndex + sentence.length;
        }
    });

    if (breaksInserted > 0) {
        textarea.value = newContent;
        const newLength = newContent.length;
        textarea.setSelectionRange(Math.min(oldSelectionStart, newLength), Math.min(oldSelectionEnd, newLength));
        textarea.scrollTop = oldScrollTop;
        textarea.focus({ preventScroll: true });
        requestAnimationFrame(() => {
           textarea.scrollTop = oldScrollTop;
        });
        const message = `Inserted <break time="500ms"/> at the start of ${breaksInserted} long sentence(s) (>${ssmlLongSentenceThreshold} words).`;
        uiUpdateSSMLStatusBar(message);
        alert(message);
    } else {
        const message = `No suitable long sentences (>${ssmlLongSentenceThreshold} words) found for inserting a break.`;
        uiUpdateSSMLStatusBar(message);
        alert(message);
        textarea.scrollTop = oldScrollTop;
        textarea.focus({ preventScroll: true });
        requestAnimationFrame(() => {
           textarea.scrollTop = oldScrollTop;
        });
    }
}

function ssmlValidate(ssmlContent) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(ssmlContent, "application/xml");
        const parserError = xmlDoc.querySelector("parsererror");
        if (parserError) {
            return { isValid: false, message: parserError.textContent || "XML parsing error." };
        }
        return { isValid: true, message: "SSML is valid." };
    } catch (e) {
        return { isValid: false, message: `Validation Error: ${e.message}` };
    }
}

export function ssmlSimpleHighlight(alreadyEscapedSsml) {
    let highlighted = ssmlEscapeXmlEntities(alreadyEscapedSsml); 
    highlighted = highlighted.replace(/(&lt;!--.*?--&gt;)/gs, '<span class="ssml-comment">$1</span>');
    highlighted = highlighted.replace(/(&lt;\/?[\w-]+)/g, '<span class="ssml-tag">$1</span>'); 
    highlighted = highlighted.replace(/(\s+[\w@.-]+)=/g, ' <span class="ssml-attr-name">$1</span>='); 
    highlighted = highlighted.replace(/(".*?")/g, '<span class="ssml-attr-value">$1</span>');
    highlighted = highlighted.replace(/(&gt;)(?!<\/span>)/g, '<span class="ssml-tag">$1</span>');
    return highlighted;
}

export function ssmlHandleSave() {
    if (!state.ssmlEditorFile) {
        uiUpdateSSMLStatusBar("No file loaded in SSML editor to save.");
        return;
    }
    const plainTextContent = dom.ssmlTextWidget.value.trim();
    if (!plainTextContent) {
        uiUpdateSSMLStatusBar("No content to save.");
        return;
    }
    uiUpdateSSMLStatusBar("Processing and saving SSML files...");

    const baseFileName = (state.ssmlEditorFile.name || "ssml_document").replace(/\.[^/.]+$/, "");
    
    const escapedContent = ssmlEscapeXmlEntities(plainTextContent);
    const fullSsml = `<speak>\n${escapedContent}\n</speak>`;
    downloadTextFile(fullSsml, `${baseFileName}_full_ssml.txt`);
    uiUpdateSSMLStatusBar(`Saved ${baseFileName}_full_ssml.txt.`);

    const breakpointChunks = plainTextContent.split(SSML_BREAKPOINT_MARKER);
    let finalChunks = [];

    breakpointChunks.forEach(bpChunk => {
        bpChunk = bpChunk.trim();
        if (!bpChunk) return;

        if (bpChunk.length > ssmlMaxLength) {
            const sentences = ssmlTokenizeSentences(bpChunk);
            let currentSubChunk = "";
            sentences.forEach(sentence => {
                if (currentSubChunk.length + sentence.length + 1 > ssmlMaxLength && currentSubChunk.length > 0) {
                    finalChunks.push(currentSubChunk.trim());
                    currentSubChunk = sentence;
                } else {
                    currentSubChunk += (currentSubChunk.length > 0 ? " " : "") + sentence;
                }
            });
            if (currentSubChunk.length > 0) {
                finalChunks.push(currentSubChunk.trim());
            }
        } else {
            finalChunks.push(bpChunk);
        }
    });
    
    if (finalChunks.length === 0 && plainTextContent.length > 0 && breakpointChunks.length === 1) { 
        finalChunks.push(plainTextContent);
    }

    finalChunks.forEach((chunk, index) => {
        let chunkToSave = chunk; 
        if (finalChunks.length > 0) { 
             if (index === 0) {
                chunkToSave = `<break time="1s"/> ${chunkToSave}`;
            }
            if (index === finalChunks.length - 1) {
                chunkToSave = `${chunkToSave} <break time="1s"/>`;
            }
        }
       
        const escapedChunk = ssmlEscapeXmlEntities(chunkToSave.trim());
        const chunkSsml = `<speak>\n${escapedChunk}\n</speak>`;
        const partFileName = finalChunks.length === 1 && breakpointChunks.length ===1 ? `${baseFileName}_ssml.txt` : `${baseFileName}_ssml_part${index + 1}.txt`;
        downloadTextFile(chunkSsml, partFileName);
        uiUpdateSSMLStatusBar(`Saved ${partFileName}.`);
    });

    if (finalChunks.length > 0) {
        uiUpdateSSMLStatusBar(`Successfully saved all SSML files.`);
    } else if (finalChunks.length === 0 && plainTextContent.length > 0 && breakpointChunks.length > 1) {
         uiUpdateSSMLStatusBar(`Content only contained breakpoints. Full SSML file saved.`);
    } else if (finalChunks.length === 0 && plainTextContent.length === 0){
        uiUpdateSSMLStatusBar(`No content to create part files. Full SSML file saved.`);
    }
}

export async function handleSSMLFileLoad(event) {
    const file = event.target.files[0];
    if (!file) return;

    const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    if (!SUPPORTED_DOC_EXTENSIONS.includes(extension)) {
        uiUpdateSSMLStatusBar(`Unsupported file type: ${extension}. Please select .txt or .docx.`);
        dom.ssmlFileInput.value = ''; 
        return;
    }

    state.setSsmlEditorFile(file);
    uiUpdateSSMLStatusBar(`Loading ${file.name} for SSML editor...`);

    const reader = new FileReader();
    reader.onload = async (e) => {
        let rawContent = "";
        if (file.name.endsWith('.txt')) {
            rawContent = e.target.result;
        } else if (file.name.endsWith('.docx')) {
            if (window.mammoth) {
                try {
                    const result = await window.mammoth.extractRawText({ arrayBuffer: e.target.result });
                    rawContent = result.value;
                } catch (err) {
                    console.error('Error extracting text from DOCX for SSML editor:', err);
                    rawContent = "Error extracting text from DOCX.";
                    uiUpdateSSMLStatusBar("Error loading DOCX content for SSML editor.");
                }
            } else {
                rawContent = "Mammoth.js not available to extract DOCX text.";
                uiUpdateSSMLStatusBar("Error: Mammoth.js not found for DOCX processing.");
            }
        }
        state.setSsmlEditorRawContent(rawContent);
        dom.ssmlTextWidget.value = state.ssmlEditorRawContent; // Load raw content directly
        dom.ssmlEditorFileInfo.textContent = `Editing: ${file.name}`;
        uiUpdateSSMLStatusBar(`Loaded ${file.name}. Ready for SSML editing.`);
        dom.ssmlSaveBtn.disabled = false;
        dom.ssmlTextWidget.scrollTop = 0; 
    };

    reader.onerror = () => {
        uiUpdateSSMLStatusBar(`Error reading file: ${file.name}`);
        state.setSsmlEditorFile(null);
        state.setSsmlEditorRawContent("");
        dom.ssmlTextWidget.value = "";
        dom.ssmlEditorFileInfo.textContent = "Error loading document.";
        dom.ssmlSaveBtn.disabled = true;
    };

    if (file.name.endsWith('.txt')) {
        reader.readAsText(file);
    } else if (file.name.endsWith('.docx')) {
        reader.readAsArrayBuffer(file);
    }
    dom.ssmlFileInput.value = ''; 
}

export function initSsmEditorEventListeners(uiHelpers) {
    uiShowSSMLModal = uiHelpers.showSSMLModal;
    uiHideSSMLModal = uiHelpers.hideSSMLModal;
    uiUpdateSSMLStatusBar = uiHelpers.updateSSMLStatusBar;

    if (dom.ssmlFileInput) {
        dom.ssmlFileInput.addEventListener('change', handleSSMLFileLoad);
    }
    if (dom.ssmlAddParagraphBtn) {
        dom.ssmlAddParagraphBtn.addEventListener('click', () => ssmlWrapSelectedText("<p>", "</p>"));
    }
    if (dom.ssmlAddSentenceBtn) {
        dom.ssmlAddSentenceBtn.addEventListener('click', () => ssmlWrapSelectedText("<s>", "</s>"));
    }
    if (dom.ssmlInsertBreakpointBtn) {
        dom.ssmlInsertBreakpointBtn.addEventListener('click', () => ssmlInsertText(SSML_BREAKPOINT_MARKER));
    }
    if (dom.ssmlPreviewBtn) {
        dom.ssmlPreviewBtn.addEventListener('click', () => {
            const plainTextContent = dom.ssmlTextWidget.value;
            const escapedContent = ssmlEscapeXmlEntities(plainTextContent);
            const fullSsml = `<speak>\n${escapedContent}\n</speak>`;

            // Only proceed if all required modal elements exist
            if (dom.ssmlPreviewValidationStatus && dom.ssmlPreviewCharCount && dom.ssmlPreviewContent && dom.ssmlPreviewModal) {
                const validation = ssmlValidate(fullSsml);
                dom.ssmlPreviewValidationStatus.textContent = validation.message;
                dom.ssmlPreviewValidationStatus.style.color = validation.isValid ? "green" : "red";
                dom.ssmlPreviewCharCount.textContent = `Character count (content only): ${plainTextContent.length} (Max length setting: ${ssmlMaxLength})`;
                if (plainTextContent.length > ssmlMaxLength) dom.ssmlPreviewCharCount.style.color = "red";
                else dom.ssmlPreviewCharCount.style.color = "inherit";

                dom.ssmlPreviewContent.innerHTML = ssmlSimpleHighlight(fullSsml);
                uiShowSSMLModal(dom.ssmlPreviewModal);
            } else {
                // Fallback: show SSML in existing modal
                if (dom.viewSSMLTextModal && dom.viewSSMLTextModalTitle && dom.viewSSMLModalContentArea) {
                    dom.viewSSMLTextModalTitle.textContent = 'SSML Preview';
                    dom.viewSSMLModalContentArea.innerHTML = ssmlSimpleHighlight(fullSsml);
                    uiShowSSMLModal(dom.viewSSMLTextModal);
                } else {
                    uiUpdateSSMLStatusBar('Preview modal not available. SSML generated successfully.');
                }
            }
        });
    }
    if (dom.ssmlPauseQuoteBtn) {
        dom.ssmlPauseQuoteBtn.addEventListener('click', () => ssmlAutoPause(/Quote|End quote/i, "Quote/End quote"));
    }
    if (dom.ssmlPauseFootnoteBtn) {
        dom.ssmlPauseFootnoteBtn.addEventListener('click', () => ssmlAutoPause(/Footnote|End footnote/i, "Footnote/End footnote"));
    }
    if (dom.ssmlBreakLongSentenceBtn) {
        dom.ssmlBreakLongSentenceBtn.addEventListener('click', ssmlAutoBreakLongSentences);
    }

    if (dom.ssmlSetMaxLengthBtn) {
        dom.ssmlSetMaxLengthBtn.addEventListener('click', () => {
            if (dom.ssmlMaxLengthInput && dom.ssmlSetMaxLengthModal) {
                dom.ssmlMaxLengthInput.value = ssmlMaxLength;
                uiShowSSMLModal(dom.ssmlSetMaxLengthModal);
            } else {
                uiUpdateSSMLStatusBar('Max length setting not available.');
            }
        });
    }
    if (dom.ssmlSetLongSentenceThresholdBtn) {
        dom.ssmlSetLongSentenceThresholdBtn.addEventListener('click', () => {
            if (dom.ssmlLongSentenceThresholdInput && dom.ssmlSetLongSentenceThresholdModal) {
                dom.ssmlLongSentenceThresholdInput.value = ssmlLongSentenceThreshold;
                uiShowSSMLModal(dom.ssmlSetLongSentenceThresholdModal);
            } else {
                uiUpdateSSMLStatusBar('Long sentence threshold setting not available.');
            }
        });
    }
    // Add event listeners only if elements exist
    if (dom.ssmlBreak03sBtn) {
        dom.ssmlBreak03sBtn.addEventListener('click', () => ssmlInsertText('<break time="0.3s"/>'));
    }
    if (dom.ssmlBreak05sBtn) {
        dom.ssmlBreak05sBtn.addEventListener('click', () => ssmlInsertText('<break time="500ms"/>'));
    }
    if (dom.ssmlBreak1sBtn) {
        dom.ssmlBreak1sBtn.addEventListener('click', () => ssmlInsertText('<break time="1s"/>'));
    }
    if (dom.ssmlBreak15sBtn) {
        dom.ssmlBreak15sBtn.addEventListener('click', () => ssmlInsertText('<break time="1.5s"/>'));
    }
    if (dom.ssmlBreak2sBtn) {
        dom.ssmlBreak2sBtn.addEventListener('click', () => ssmlInsertText('<break time="2s"/>'));
    }
    if (dom.ssmlSaveBtn) {
        dom.ssmlSaveBtn.addEventListener('click', ssmlHandleSave);
        dom.ssmlSaveBtn.disabled = !state.ssmlEditorFile;
    }

    dom.ssmlFindNextBtn.addEventListener('click', () => {
        const textarea = dom.ssmlTextWidget;
        const oldScrollTop = textarea.scrollTop; 
        const searchTerm = dom.ssmlFindInput.value;
        if (!searchTerm) {
            textarea.focus({ preventScroll: true }); 
            requestAnimationFrame(() => { textarea.scrollTop = oldScrollTop; });
            return;
        }
        const content = textarea.value;
        let searchStartIndex = textarea.selectionEnd;
        if (textarea.selectionStart === textarea.selectionEnd && textarea.selectionStart < ssmlLastSearchIndex) {
             searchStartIndex = ssmlLastSearchIndex; 
        }
        const foundIndex = content.indexOf(searchTerm, searchStartIndex);
        if (foundIndex !== -1) {
            textarea.focus({ preventScroll: true });
            textarea.setSelectionRange(foundIndex, foundIndex + searchTerm.length);
            ssmlLastSearchIndex = foundIndex + searchTerm.length; 
            uiUpdateSSMLStatusBar(`Found "${searchTerm}".`);
        } else {
            const foundFromStart = content.indexOf(searchTerm, 0);
            if (foundFromStart !== -1) {
                textarea.focus({ preventScroll: true });
                textarea.setSelectionRange(foundFromStart, foundFromStart + searchTerm.length);
                ssmlLastSearchIndex = foundFromStart + searchTerm.length;
                uiUpdateSSMLStatusBar(`Found "${searchTerm}" (from beginning).`);
            } else {
                ssmlLastSearchIndex = 0; 
                uiUpdateSSMLStatusBar(`"${searchTerm}" not found.`);
                textarea.focus({ preventScroll: true });
            }
        }
        textarea.scrollTop = oldScrollTop; 
        requestAnimationFrame(() => { textarea.scrollTop = oldScrollTop; });
    });

    dom.ssmlReplaceBtn.addEventListener('click', () => {
        const textarea = dom.ssmlTextWidget;
        const oldScrollTop = textarea.scrollTop;
        const searchTerm = dom.ssmlFindInput.value;
        const replaceTerm = dom.ssmlReplaceInput.value;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        if (searchTerm && textarea.value.substring(start,end) === searchTerm) {
            const currentText = textarea.value;
            textarea.value = currentText.substring(0,start) + replaceTerm + currentText.substring(end);
            const newCursorPos = start + replaceTerm.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            ssmlLastSearchIndex = newCursorPos; 
            uiUpdateSSMLStatusBar(`Replaced one instance of "${searchTerm}".`);
        } else {
            uiUpdateSSMLStatusBar(`Select an instance of "${searchTerm}" to replace, or use "Find Next".`);
        }
        textarea.focus({ preventScroll: true });
        textarea.scrollTop = oldScrollTop;
        requestAnimationFrame(() => { textarea.scrollTop = oldScrollTop; });
    });

    dom.ssmlReplaceAllBtn.addEventListener('click', () => {
        const textarea = dom.ssmlTextWidget;
        const oldScrollTop = textarea.scrollTop;
        const oldSelectionStart = textarea.selectionStart; 
        const searchTerm = dom.ssmlFindInput.value;
        const replaceTerm = dom.ssmlReplaceInput.value;
        if (!searchTerm) {
            textarea.focus({ preventScroll: true });
            requestAnimationFrame(() => { textarea.scrollTop = oldScrollTop; });
            return;
        }
        let oldContent = textarea.value;
        let newContent = oldContent.split(searchTerm).join(replaceTerm);
        if (oldContent !== newContent) {
            textarea.value = newContent;
            uiUpdateSSMLStatusBar(`Replaced all instances of "${searchTerm}".`);
        } else {
            uiUpdateSSMLStatusBar(`"${searchTerm}" not found.`);
        }
        ssmlLastSearchIndex = 0;
        const newLength = newContent.length;
        const newCursorPos = Math.min(oldSelectionStart, newLength); 
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus({ preventScroll: true });
        textarea.scrollTop = oldScrollTop;
        requestAnimationFrame(() => { textarea.scrollTop = oldScrollTop; });
    });
    if (dom.ssmlFindInput) {
        dom.ssmlFindInput.addEventListener('input', () => { ssmlLastSearchIndex = 0; });
    }
    if (dom.ssmlMaxLengthConfirmBtn && dom.ssmlMaxLengthInput && dom.ssmlSetMaxLengthModal) {
        dom.ssmlMaxLengthConfirmBtn.addEventListener('click', () => {
            const val = parseInt(dom.ssmlMaxLengthInput.value, 10);
            if (!isNaN(val) && val >= SSML_MIN_MAX_LENGTH) {
                ssmlMaxLength = val;
                uiUpdateSSMLStatusBar(`Max length set to ${ssmlMaxLength}.`);
                uiHideSSMLModal(dom.ssmlSetMaxLengthModal);
            } else {
                alert(`Invalid input. Minimum is ${SSML_MIN_MAX_LENGTH}.`);
            }
        });
    }
    if (dom.ssmlMaxLengthCancelBtn && dom.ssmlSetMaxLengthModal) {
        dom.ssmlMaxLengthCancelBtn.addEventListener('click', () => uiHideSSMLModal(dom.ssmlSetMaxLengthModal));
    }
    if (dom.ssmlLongSentenceConfirmBtn && dom.ssmlLongSentenceThresholdInput && dom.ssmlSetLongSentenceThresholdModal) {
        dom.ssmlLongSentenceConfirmBtn.addEventListener('click', () => {
            const val = parseInt(dom.ssmlLongSentenceThresholdInput.value, 10);
            if (!isNaN(val) && val >= SSML_MIN_LONG_SENTENCE_THRESHOLD) {
                ssmlLongSentenceThreshold = val;
                uiUpdateSSMLStatusBar(`Long sentence threshold set to ${ssmlLongSentenceThreshold}.`);
                uiHideSSMLModal(dom.ssmlSetLongSentenceThresholdModal);
            } else {
                alert(`Invalid input. Minimum is ${SSML_MIN_LONG_SENTENCE_THRESHOLD}.`);
            }
        });
    }
    if (dom.ssmlLongSentenceCancelBtn && dom.ssmlSetLongSentenceThresholdModal) {
        dom.ssmlLongSentenceCancelBtn.addEventListener('click', () => uiHideSSMLModal(dom.ssmlSetLongSentenceThresholdModal));
    }
    if (dom.ssmlPreviewCloseBtn && dom.ssmlPreviewModal) {
        dom.ssmlPreviewCloseBtn.addEventListener('click', () => uiHideSSMLModal(dom.ssmlPreviewModal));
    }
    if (dom.ssmlTextWidget) {
        dom.ssmlTextWidget.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key.toLowerCase() === 'p') { e.preventDefault(); ssmlWrapSelectedText("<p>", "</p>"); } 
        else if (e.ctrlKey && e.key.toLowerCase() === 's' && !e.shiftKey) { e.preventDefault(); ssmlWrapSelectedText("<s>", "</s>"); } 
        else if (e.ctrlKey && e.key.toLowerCase() === 'b') { e.preventDefault(); ssmlInsertText(SSML_BREAKPOINT_MARKER); } 
        else if (e.ctrlKey && e.key.toLowerCase() === 'r') { e.preventDefault(); if (dom.ssmlPreviewBtn) dom.ssmlPreviewBtn.click(); }
        else if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 's') { e.preventDefault(); if (dom.ssmlSaveBtn && !dom.ssmlSaveBtn.disabled) ssmlHandleSave(); }
        else if (e.key === 'F3') { e.preventDefault(); if (dom.ssmlFindNextBtn) dom.ssmlFindNextBtn.click(); }
        else if (e.altKey) {
            switch (e.key) {
                case '1': e.preventDefault(); if (dom.ssmlBreak03sBtn) dom.ssmlBreak03sBtn.click(); break;
                case '2': e.preventDefault(); if (dom.ssmlBreak05sBtn) dom.ssmlBreak05sBtn.click(); break;
                case '3': e.preventDefault(); if (dom.ssmlBreak1sBtn) dom.ssmlBreak1sBtn.click(); break;
                case '4': e.preventDefault(); if (dom.ssmlBreak15sBtn) dom.ssmlBreak15sBtn.click(); break;
                case '5': e.preventDefault(); if (dom.ssmlBreak2sBtn) dom.ssmlBreak2sBtn.click(); break;
            }
        } else if (e.ctrlKey && e.key.toLowerCase() === 'o') { e.preventDefault(); if (dom.ssmlFileInputLabel) dom.ssmlFileInputLabel.click(); }
        });
    }
}
