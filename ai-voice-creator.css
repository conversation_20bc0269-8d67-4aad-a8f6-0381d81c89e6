/* AI Voice Creator View Styles */
.ai-voice-creator-view {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 15px;
    background-color: var(--bg-ssml-editor-view);
    box-sizing: border-box;
    overflow: hidden;
    gap: 10px;
}

.ai-voice-creator-view h2 {
    font-size: 1.5em;
    color: var(--text-panel-heading);
    margin-bottom: 5px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-primary);
}

#ai-voice-ssml-loaded-file-info {
    font-size: 0.9em;
    color: var(--text-ssml-editor-file-info);
    margin-bottom: 10px;
    min-height: 1.2em;
}

.ai-voice-creator-layout {
    display: flex;
    gap: 15px;
    flex-grow: 1;
    overflow: hidden;
    min-height: 0;
}

/* Left Controls Panel */
.ai-voice-controls-panel {
    flex: 0 0 320px;
    background-color: var(--bg-panel);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px var(--shadow-light);
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

.ai-voice-control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-secondary);
}
.ai-voice-control-group:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.ai-voice-control-group h3 {
    font-size: 1em;
    color: var(--text-panel-heading);
    margin-bottom: 8px;
}

.ai-voice-control-group label {
    font-size: 0.9em;
    color: var(--text-secondary);
}

.ai-voice-control-group select,
.ai-voice-control-group input[type="text"] {
    width: 100%;
    padding: 8px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-input);
    font-size: 0.9em;
    background-color: var(--bg-input);
    color: var(--text-primary);
    box-sizing: border-box;
}

.radio-group {
    display: flex;
    gap: 15px;
    align-items: center;
}
.radio-group label {
    cursor: pointer;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
}
.slider-container input[type="range"] {
    flex-grow: 1;
    accent-color: var(--accent-primary);
}

/* Right Main Panel */
.ai-voice-main-panel {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 0;
    overflow: hidden;
}

.ai-voice-textarea-wrapper {
    flex: 3 1 0; /* Takes up 3/4 of the space */
    display: flex;
    min-height: 0;
}

#ai-voice-ssml-textarea {
    width: 100%;
    height: 100%;
    border: 1px solid var(--border-ssml-text-widget);
    border-radius: 4px;
    padding: 10px;
    font-family: "Consolas", "Monaco", monospace;
    font-size: 0.95em;
    box-sizing: border-box;
    resize: none;
    background-color: var(--bg-input);
    color: var(--text-primary);
}

.ai-voice-status-log-wrapper {
    flex: 1 1 0; /* Takes up 1/4 of the space */
    display: flex;
    flex-direction: column;
    min-height: 0;
    background-color: var(--bg-panel);
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 1px 3px var(--shadow-light);
}

.status-log {
    flex-grow: 1;
    overflow-y: auto;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    padding: 8px;
    font-size: 0.85em;
    border-radius: 4px;
}

.status-log p {
    margin: 0 0 5px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-secondary);
    word-break: break-word;
}
.status-log p:last-child {
    border-bottom: none;
}
.status-log p.error {
    color: var(--status-error-color);
}
.status-log p.success {
    color: var(--status-ok-color);
}