<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .nav-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .nav-button.active { background: #0056b3; }
        .view { display: none; padding: 20px; border: 1px solid #ccc; margin: 10px 0; }
        .view.active { display: block; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Navigation Test</h1>
    
    <div>
        <button id="test-verification-btn" class="nav-button">Audiobook Verification</button>
        <button id="test-text-editor-btn" class="nav-button">Audiobook Text Editor</button>
        <button id="test-ai-voice-btn" class="nav-button">AI Voice Creator</button>
    </div>
    
    <div id="test-verification-view" class="view">
        <h2>Audiobook Verification View</h2>
        <p>This is the verification view content.</p>
    </div>
    
    <div id="test-text-editor-view" class="view">
        <h2>Audiobook Text Editor View</h2>
        <p>This is the text editor view content.</p>
        <textarea placeholder="Test text area for SSML editing..." style="width: 100%; height: 200px;"></textarea>
    </div>
    
    <div id="test-ai-voice-view" class="view">
        <h2>AI Voice Creator View</h2>
        <p>This is the AI voice creator view content.</p>
    </div>
    
    <div id="test-results"></div>
    
    <script>
        // Simple navigation test
        const buttons = {
            verification: document.getElementById('test-verification-btn'),
            textEditor: document.getElementById('test-text-editor-btn'),
            aiVoice: document.getElementById('test-ai-voice-btn')
        };
        
        const views = {
            verification: document.getElementById('test-verification-view'),
            textEditor: document.getElementById('test-text-editor-view'),
            aiVoice: document.getElementById('test-ai-voice-view')
        };
        
        const resultsDiv = document.getElementById('test-results');
        
        function showView(viewName) {
            // Hide all views
            Object.values(views).forEach(view => view.classList.remove('active'));
            Object.values(buttons).forEach(btn => btn.classList.remove('active'));
            
            // Show selected view
            if (views[viewName]) {
                views[viewName].classList.add('active');
                buttons[viewName].classList.add('active');
                
                resultsDiv.innerHTML += `<div class="test-result success">✓ Successfully switched to ${viewName} view</div>`;
            } else {
                resultsDiv.innerHTML += `<div class="test-result error">✗ Failed to switch to ${viewName} view</div>`;
            }
        }
        
        // Add event listeners
        buttons.verification.addEventListener('click', () => showView('verification'));
        buttons.textEditor.addEventListener('click', () => showView('textEditor'));
        buttons.aiVoice.addEventListener('click', () => showView('aiVoice'));
        
        // Initialize with verification view
        showView('verification');
        
        // Test the text editor specifically
        setTimeout(() => {
            resultsDiv.innerHTML += '<div class="test-result">Testing text editor navigation...</div>';
            showView('textEditor');
            
            setTimeout(() => {
                const textArea = document.querySelector('#test-text-editor-view textarea');
                if (textArea && textArea.offsetParent !== null) {
                    resultsDiv.innerHTML += '<div class="test-result success">✓ Text editor view is visible and functional</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="test-result error">✗ Text editor view is not visible</div>';
                }
            }, 100);
        }, 1000);
    </script>
</body>
</html>
