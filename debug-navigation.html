<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Navigation</title>
    <style>
        .hidden-view { display: none !important; }
        .active-nav-button { background-color: #0056b3 !important; }
        .header-nav-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .view { padding: 20px; border: 1px solid #ccc; margin: 10px 0; }
        #debug-log { background: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Debug Navigation Test</h1>
    
    <div class="app-header">
        <button id="audiobook-verification-btn" class="header-nav-button">Audiobook Verification</button>
        <button id="audiobook-text-editor-btn" class="header-nav-button">Audiobook Text Editor</button>
        <button id="ai-voice-creator-btn" class="header-nav-button">AI Voice Creator</button>
    </div>
    
    <div id="app-container" class="view">
        <h2>Audiobook Verification View</h2>
        <p>This is the main verification view.</p>
    </div>
    
    <div id="text-editor-view-container" class="view hidden-view">
        <h2>Audiobook SSML Editor</h2>
        <p>This is the text editor view.</p>
        <textarea id="ssml-text-widget" placeholder="Load a document to start editing..." style="width: 100%; height: 200px;"></textarea>
        <div id="ssml-status-bar">SSML Editor Ready.</div>
    </div>
    
    <div id="ai-voice-creator-view-container" class="view hidden-view">
        <h2>AI Voice Creator</h2>
        <p>This is the AI voice creator view.</p>
    </div>
    
    <div id="debug-log"></div>
    
    <script type="module">
        // Simulate the DOM elements structure
        const dom = {
            appContainer: document.getElementById('app-container'),
            textEditorViewContainer: document.getElementById('text-editor-view-container'),
            aiVoiceCreatorViewContainer: document.getElementById('ai-voice-creator-view-container'),
            audiobookVerificationBtn: document.getElementById('audiobook-verification-btn'),
            audiobookTextEditorBtn: document.getElementById('audiobook-text-editor-btn'),
            aiVoiceCreatorBtn: document.getElementById('ai-voice-creator-btn'),
            ssmlTextWidget: document.getElementById('ssml-text-widget')
        };
        
        const debugLog = document.getElementById('debug-log');
        
        function log(message) {
            console.log(message);
            debugLog.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        function setActiveView(viewName) {
            log(`setActiveView called with: ${viewName}`);
            
            const viewMap = {
                'verification': {
                    container: dom.appContainer,
                    button: dom.audiobookVerificationBtn
                },
                'textEditor': {
                    container: dom.textEditorViewContainer,
                    button: dom.audiobookTextEditorBtn
                },
                'aiVoiceCreator': {
                    container: dom.aiVoiceCreatorViewContainer,
                    button: dom.aiVoiceCreatorBtn
                }
            };

            const allViews = [dom.appContainer, dom.textEditorViewContainer, dom.aiVoiceCreatorViewContainer];
            const allButtons = [dom.audiobookVerificationBtn, dom.audiobookTextEditorBtn, dom.aiVoiceCreatorBtn];

            log('Available views: ' + allViews.map(v => v ? v.id : 'null').join(', '));
            log('Available buttons: ' + allButtons.map(b => b ? b.id : 'null').join(', '));

            // Hide all views
            allViews.forEach(view => {
                if (view) {
                    view.classList.add('hidden-view');
                    log(`Hidden view: ${view.id}`);
                }
            });

            // Remove active state from all buttons
            allButtons.forEach(button => {
                if (button) {
                    button.classList.remove('active-nav-button');
                    log(`Deactivated button: ${button.id}`);
                }
            });

            // Show the requested view and activate its button
            const targetView = viewMap[viewName];
            if (targetView) {
                if (targetView.container) {
                    targetView.container.classList.remove('hidden-view');
                    log(`Showed view: ${targetView.container.id}`);
                }
                if (targetView.button) {
                    targetView.button.classList.add('active-nav-button');
                    log(`Activated button: ${targetView.button.id}`);
                }
            } else {
                log(`Unknown view name: ${viewName}`);
            }
        }
        
        // Add event listeners
        dom.audiobookVerificationBtn.addEventListener('click', () => {
            log('Verification button clicked');
            setActiveView('verification');
        });
        
        dom.audiobookTextEditorBtn.addEventListener('click', () => {
            log('Text Editor button clicked');
            setActiveView('textEditor');
        });
        
        dom.aiVoiceCreatorBtn.addEventListener('click', () => {
            log('AI Voice Creator button clicked');
            setActiveView('aiVoiceCreator');
        });
        
        // Initialize
        log('Initializing with verification view');
        setActiveView('verification');
        
        // Test text editor after 2 seconds
        setTimeout(() => {
            log('Auto-testing text editor navigation...');
            setActiveView('textEditor');
        }, 2000);
    </script>
</body>
</html>
