{"name": "web-media-player-doc-editor", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"docx": "^9.5.0", "pizzip": "^3.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "typescript": "^5.5.3", "vite": "^5.3.3"}}